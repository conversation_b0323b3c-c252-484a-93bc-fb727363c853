/**
 * usePlaybackMonitor Hook
 *
 * Monitors video playback state to detect when playback gets stuck
 * (playing but seek bar not advancing). Provides automatic recovery
 * mechanism by triggering audio preloader retry.
 *
 * Features:
 * - Detects stuck playback after 3 seconds of no progress
 * - Automatic recovery with audio preloader retry
 * - Manual recovery trigger
 * - Comprehensive logging for debugging
 */

import { useEffect, useRef, useCallback, useState } from 'react'
import { PlayerRef } from '@remotion/player'

interface PlaybackState {
  isPlaying: boolean
  currentTime: number
  lastUpdateTime: number
  stuckDetectionCount: number
}

interface PlaybackMonitorOptions {
  stuckThresholdMs?: number // Time to wait before considering playback stuck
  monitorIntervalMs?: number // How often to check playback progress
  maxRecoveryAttempts?: number // Maximum automatic recovery attempts
}

interface PlaybackMonitorResult {
  isStuck: boolean
  isRecovering: boolean
  recoveryAttempts: number
  triggerRecovery: () => void
  resetMonitor: () => void
}

export const usePlaybackMonitor = (
  playerRef: React.RefObject<PlayerRef | null>,
  isPlaying: boolean,
  currentTime: number,
  onRecoveryNeeded?: () => void,
  options: PlaybackMonitorOptions = {}
): PlaybackMonitorResult => {
  const {
    stuckThresholdMs = 3000, // 3 seconds
    monitorIntervalMs = 500, // Check every 500ms
    maxRecoveryAttempts = 3
  } = options

  const [isStuck, setIsStuck] = useState(false)
  const [isRecovering, setIsRecovering] = useState(false)
  const [recoveryAttempts, setRecoveryAttempts] = useState(0)

  const stateRef = useRef<PlaybackState>({
    isPlaying: false,
    currentTime: 0,
    lastUpdateTime: Date.now(),
    stuckDetectionCount: 0
  })

  const monitorIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const recoveryTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Reset monitor state
  const resetMonitor = useCallback(() => {
    console.log('🔄 Resetting playback monitor')
    setIsStuck(false)
    setIsRecovering(false)
    setRecoveryAttempts(0)
    stateRef.current.stuckDetectionCount = 0
    stateRef.current.lastUpdateTime = Date.now()
    
    if (recoveryTimeoutRef.current) {
      clearTimeout(recoveryTimeoutRef.current)
      recoveryTimeoutRef.current = null
    }
  }, [])

  // Trigger recovery manually
  const triggerRecovery = useCallback(() => {
    if (isRecovering || recoveryAttempts >= maxRecoveryAttempts) {
      console.log('🚫 Recovery already in progress or max attempts reached')
      return
    }

    console.log(`🔧 Manual recovery triggered (attempt ${recoveryAttempts + 1}/${maxRecoveryAttempts})`)
    setIsRecovering(true)
    setRecoveryAttempts(prev => prev + 1)

    // Call recovery callback
    onRecoveryNeeded?.()

    // Reset recovery state after a delay
    recoveryTimeoutRef.current = setTimeout(() => {
      setIsRecovering(false)
      console.log('🔧 Recovery attempt completed')
    }, 5000) // 5 second recovery timeout
  }, [isRecovering, recoveryAttempts, maxRecoveryAttempts, onRecoveryNeeded])

  // Monitor playback progress
  const checkPlaybackProgress = useCallback(() => {
    if (!playerRef?.current || !isPlaying) {
      // Reset stuck detection when not playing
      if (stateRef.current.stuckDetectionCount > 0) {
        stateRef.current.stuckDetectionCount = 0
        setIsStuck(false)
      }
      return
    }

    const now = Date.now()
    const timeSinceLastUpdate = now - stateRef.current.lastUpdateTime
    const currentPlayerTime = currentTime

    // Check if time has advanced
    const timeAdvanced = currentPlayerTime > stateRef.current.currentTime
    const significantTimeGap = Math.abs(currentPlayerTime - stateRef.current.currentTime) > 0.1

    if (timeAdvanced || significantTimeGap) {
      // Progress detected - reset stuck detection
      if (stateRef.current.stuckDetectionCount > 0) {
        console.log('✅ Playback progress detected, resetting stuck detection')
        stateRef.current.stuckDetectionCount = 0
        setIsStuck(false)
      }
      
      stateRef.current.currentTime = currentPlayerTime
      stateRef.current.lastUpdateTime = now
    } else if (timeSinceLastUpdate > stuckThresholdMs) {
      // No progress for threshold time - increment stuck detection
      stateRef.current.stuckDetectionCount++
      
      if (stateRef.current.stuckDetectionCount === 1) {
        console.warn('⚠️ Stuck playback detected - no progress for', stuckThresholdMs, 'ms')
        setIsStuck(true)
        
        // Trigger automatic recovery if not already recovering and under max attempts
        if (!isRecovering && recoveryAttempts < maxRecoveryAttempts) {
          console.log('🔧 Triggering automatic recovery')
          triggerRecovery()
        } else if (recoveryAttempts >= maxRecoveryAttempts) {
          console.error('❌ Max recovery attempts reached, manual intervention required')
        }
      }
      
      // Update last update time to prevent rapid stuck detection
      stateRef.current.lastUpdateTime = now
    }

    // Update state
    stateRef.current.isPlaying = isPlaying
  }, [playerRef, isPlaying, currentTime, stuckThresholdMs, isRecovering, recoveryAttempts, maxRecoveryAttempts, triggerRecovery])

  // Start/stop monitoring based on playback state
  useEffect(() => {
    if (isPlaying && !monitorIntervalRef.current) {
      console.log('🎯 Starting playback monitoring')
      monitorIntervalRef.current = setInterval(checkPlaybackProgress, monitorIntervalMs)
    } else if (!isPlaying && monitorIntervalRef.current) {
      console.log('⏸️ Stopping playback monitoring')
      clearInterval(monitorIntervalRef.current)
      monitorIntervalRef.current = null
    }

    return () => {
      if (monitorIntervalRef.current) {
        clearInterval(monitorIntervalRef.current)
        monitorIntervalRef.current = null
      }
    }
  }, [isPlaying, checkPlaybackProgress, monitorIntervalMs])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (monitorIntervalRef.current) {
        clearInterval(monitorIntervalRef.current)
      }
      if (recoveryTimeoutRef.current) {
        clearTimeout(recoveryTimeoutRef.current)
      }
    }
  }, [])

  // Reset when player changes
  useEffect(() => {
    resetMonitor()
  }, [playerRef, resetMonitor])

  return {
    isStuck,
    isRecovering,
    recoveryAttempts,
    triggerRecovery,
    resetMonitor
  }
}
